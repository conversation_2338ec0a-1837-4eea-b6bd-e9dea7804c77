from typing import Optional

from app.index import get_index
from llama_index.core.agent.workflow import Agent<PERSON>ork<PERSON>
from llama_index.core.settings import Settings
from llama_index.server.api.models import ChatRequest
from llama_index.server.tools.index import get_query_engine_tool
from llama_index.server.tools.index.citation import (
    CITATION_SYSTEM_PROMPT,
    enable_citation,
)


def create_workflow(chat_request: Optional[ChatRequest] = None) -> AgentWorkflow:
    index = get_index(chat_request=chat_request)
    if index is None:
        raise RuntimeError(
            "Index not found! Please run `uv run generate` to index the data first."
        )

    # Create a query engine with minimal similarity_top_k to avoid duplicate responses
    query_engine = index.as_query_engine(
        similarity_top_k=1,  # Only use the most relevant document chunk
        response_mode="compact"  # Use compact mode for cleaner responses
    )

    # Create a query tool with citations enabled
    query_tool = enable_citation(get_query_engine_tool(
        index=index,
        query_engine=query_engine
    ))

    # Define the system prompt for the agent
    # Append the citation system prompt to the system prompt
    system_prompt = """You are a helpful financial knowledge assistant.

    CRITICAL INSTRUCTIONS:
    1. Provide EXACTLY ONE clear, complete answer to each question
    2. NEVER repeat your response multiple times
    3. NEVER include "Response 1:", "Response 2:" or any similar labels
    4. NEVER use separators like "---------------------" between responses
    5. Use citations in the format [citation:id] when referencing documents
    6. Keep your answer focused and avoid any redundancy
    7. If you find similar information from multiple sources, synthesize it into ONE coherent response

    """
    system_prompt += CITATION_SYSTEM_PROMPT

    return AgentWorkflow.from_tools_or_functions(
        tools_or_functions=[query_tool],
        llm=Settings.llm,
        system_prompt=system_prompt,
    )
